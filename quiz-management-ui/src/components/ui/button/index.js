import { cva } from 'class-variance-authority';

export { default as <PERSON><PERSON> } from './Button.vue';

export const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
  {
    variants: {
      variant: {
        default:
          'bg-[rgb(103,232,249)] text-[rgb(31,41,55)] shadow hover:bg-[rgba(103,232,249,0.8)]',
        destructive:
          'bg-[rgb(103,232,249)] text-[rgb(31,41,55)] shadow-sm hover:bg-[rgba(103,232,249,0.8)]',
        outline:
          'border border-[rgb(103,232,249)] bg-background shadow-sm hover:bg-[rgb(103,232,249)] hover:text-[rgb(31,41,55)]',
        secondary:
          'bg-[rgb(103,232,249)] text-[rgb(31,41,55)] shadow-sm hover:bg-[rgba(103,232,249,0.8)]',
        ghost: 'hover:bg-[rgb(103,232,249)] hover:text-[rgb(31,41,55)]',
        link: 'text-[rgb(103,232,249)] underline-offset-4 hover:underline',

        transparentHome:
          '',

        skillBack:
          'group relative px-6 py-3 overflow-hidden rounded-lg border border-cyan-500 bg-transparent text-cyan-400 font-medium shadow-md hover:bg-cyan-500 hover:text-white transition-all duration-300 transform hover:-translate-y-1',
        skillGenerate:
          'group relative px-6 py-3 overflow-hidden rounded-xl bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-medium shadow-lg hover:shadow-cyan-500/30 transition-all duration-300 transform hover:-translate-y-1',
        skillAdd:
          'group relative overflow-hidden rounded-lg bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-medium shadow-md hover:shadow-cyan-500/30 transition-all duration-300 transform hover:-translate-y-1',
        skillNav:
          'text-cyan-400 hover:text-cyan-300 transition-colors',
        assessmentGenerate:
          'group relative overflow-hidden rounded-xl bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-medium shadow-lg hover:shadow-cyan-500/30 transition-all duration-300 transform hover:-translate-y-1',
        sessionGenerate:
          'group relative overflow-hidden rounded-xl bg-gradient-to-r from-teal-500 to-cyan-600 text-white font-medium shadow-lg hover:shadow-teal-500/30 transition-all duration-300 transform hover:-translate-y-1',
        reportGenerate:
          'group relative overflow-hidden rounded-xl bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-medium shadow-lg hover:shadow-cyan-500/30 transition-all duration-300 transform hover:-translate-y-1',
        assessmentBack:
          'group relative overflow-hidden rounded-lg border border-cyan-500 bg-transparent text-cyan-400 font-medium shadow-md hover:bg-cyan-500 hover:text-white transition-all duration-300 transform hover:-translate-y-1',
        generalAction:
          'group relative overflow-hidden rounded bg-gradient-to-r from-cyan-600 to-blue-700 text-white font-medium shadow-md hover:shadow-cyan-500/30 transition-all duration-300 transform hover:-translate-y-1',
        quickAction:
          'flex flex-col items-center p-4 bg-gray-900/60 backdrop-blur-md rounded-lg border border-gray-800 hover:border-cyan-500/50 transition-all duration-300 shadow-glow-sm group',
        homeCardButton:
          'w-full text-left px-4 py-3 rounded-lg bg-gray-800/80 text-gray-200 hover:text-white transition-all flex items-center group',
        homeCardNav:
          'w-full text-left px-4 py-3 rounded-lg bg-gray-800/80 hover:bg-gradient-to-r text-gray-200 hover:text-white transition-all flex items-center group',
      },
      size: {
        default: 'w-full py-3 px-6',
        xs: 'h-7 rounded px-2',
        sm: 'h-8 rounded-md px-3 text-xs',
        homeNav: 'p-1',
        lg: 'h-10 rounded-md px-8',
        icon: 'h-9 w-9',
        skillButton: 'px-6 py-3',
        skillNav: 'h-6 w-6',
        quickAction: 'p-4',
        homeCardButton: 'px-4 py-3',
        homeCardNav: 'px-4 py-3',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

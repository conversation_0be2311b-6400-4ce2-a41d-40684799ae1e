<template>
  <HerbitProfessionalLayout
    title="Assessment-wise Report"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >

      <!-- Form Card -->
      <FormCard color="cyan">
            <form @submit.prevent="generateReport" class="space-y-6">
              <!-- Assessment Base Name Input -->
              <div>
                <Label for="assessmentBaseName" class="text-gray-300">Assessment Base Name</Label>
                <Input
                  id="assessmentBaseName"
                  v-model="assessmentBaseName"
                  placeholder="e.g. DevOps_Basics_10_08_2024"
                  required
                />
                <p class="text-xs text-gray-400 mt-1">Enter the base name of the assessment (e.g. MyAssessment_DD_MM_YYYY)</p>
              </div>

              <!-- Quiz Type Selection -->
              <div>
                <Label for="quizType" class="text-gray-300">Assessment Type</Label>
                <select
                  id="quizType"
                  v-model="quizType"
                  class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
                  required
                >
                  <option value="mock">Mock Assessment</option>
                  <option value="final">Final Assessment</option>
                </select>
              </div>

              <!-- Loading indicator -->
              <div v-if="isLoading" class="flex justify-center items-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-cyan-500"></div>
                <span class="ml-3 text-gray-300">Generating report...</span>
              </div>

              <!-- Error message -->
              <Alert v-if="errorMessage" variant="error">
                <AlertDescription>{{ errorMessage }}</AlertDescription>
              </Alert>

              <!-- Success message and download links -->
              <div v-if="reportGenerated" class="space-y-4">
                <Alert variant="success">
                  <AlertDescription>Report generated successfully!</AlertDescription>
                </Alert>

                <div class="space-y-3">
                  <h3 class="text-white font-medium">Download Reports:</h3>

                  <Card v-if="baseReportUrl" variant="download" color="cyan" size="sm">
                    <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-cyan-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <div class="flex-1">
                      <p class="text-white">Base Report</p>
                      <p class="text-xs text-gray-400">Contains detailed assessment data</p>
                    </div>
                    <Button
                      :as="'a'"
                      :href="baseReportUrl"
                      download
                      variant="generalAction"
                      size="skillButton"
                    >
                      Download
                    </Button>
                    </div>
                  </Card>

                  <Card v-if="scoreReportUrl" variant="download" color="blue" size="sm">
                    <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <div class="flex-1">
                      <p class="text-white">Score Report</p>
                      <p class="text-xs text-gray-400">Contains score summaries and statistics</p>
                    </div>
                    <Button
                      :as="'a'"
                      :href="scoreReportUrl"
                      download
                      variant="generalAction"
                      size="skillButton"
                    >
                      Download
                    </Button>
                    </div>
                  </Card>
                </div>
              </div>

              <!-- Submit button -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  variant="reportGenerate"
                  size="skillButton"
                  :disabled="isLoading"
                >
                  <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    Generate Assessment Report
                  </span>
                </Button>
              </div>
            </form>
      </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import { HerbitProfessionalLayout } from '@/components/layout';
import { FormCard } from '@/components/ui/form-card';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Form data
const assessmentBaseName = ref('');
const quizType = ref('mock');
const isLoading = ref(false);
const errorMessage = ref('');
const reportGenerated = ref(false);
const baseReportUrl = ref('');
const scoreReportUrl = ref('');

// Generate report function
const generateReport = async () => {
  isLoading.value = true;
  errorMessage.value = '';
  reportGenerated.value = false;
  baseReportUrl.value = '';
  scoreReportUrl.value = '';

  try {
    const response = await axios.post('/api/admin/reports', {
      report_type: 'topic_wise',
      report_topic: assessmentBaseName.value,
      quiz_type: quizType.value
    });

    const data = response.data;

    if (data.message && !data.base_report && !data.score_report) {
      // No data found for the assessment
      errorMessage.value = data.message;
      return;
    }

    reportGenerated.value = true;

    // Create blob URLs for the reports
    if (data.base_report) {
      const blob = new Blob([data.base_report], { type: 'text/csv' });
      baseReportUrl.value = URL.createObjectURL(blob);
    }

    if (data.score_report) {
      const blob = new Blob([data.score_report], { type: 'text/csv' });
      scoreReportUrl.value = URL.createObjectURL(blob);
    }

  } catch (error) {
    errorMessage.value = error.message || 'An error occurred while generating the report';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* Animation for floating particles */
@keyframes float-particle {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-30px) translateX(15px);
  }
  50% {
    transform: translateY(-15px) translateX(30px);
  }
  75% {
    transform: translateY(20px) translateX(-10px);
  }
}

/* Animation for glowing orbs */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}

/* Grid pattern */
.pattern-grid-lg {
  background-image: linear-gradient(rgba(103, 232, 249, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(103, 232, 249, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
}
</style>

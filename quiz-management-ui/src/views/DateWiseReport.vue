<template>
  <HerbitProfessionalLayout
    title="Date-wise Report"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Form Card -->
    <FormCard color="teal">
          <form @submit.prevent="generateReport" class="space-y-6">
            <!-- Date Input -->
            <div>
              <Label for="reportDate" class="text-gray-300">Report Date (DD-MM-YYYY)</Label>
              <Input
                id="reportDate"
                v-model="reportDate"
                placeholder="DD-MM-YYYY"
              />
              <p class="text-xs text-gray-400 mt-1">Leave empty to use today's date: {{ today }}</p>
            </div>

            <!-- Quiz Type Selection -->
            <div>
              <Label for="quizType" class="text-gray-300">Assessment Type</Label>
              <select
                id="quizType"
                v-model="quizType"
                class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              >
                <option value="mock">Mock Assessments</option>
                <option value="final">Final Assessments</option>
              </select>
            </div>

            <!-- Loading indicator -->
            <div v-if="isLoading" class="flex justify-center items-center py-4">
              <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-teal-500"></div>
              <span class="ml-3 text-gray-300">Generating report...</span>
            </div>

            <!-- Error message -->
            <Alert v-if="errorMessage" variant="error">
              <AlertDescription>{{ errorMessage }}</AlertDescription>
            </Alert>

            <!-- Success message and download links -->
            <div v-if="reportGenerated" class="space-y-4">
              <Alert variant="success">
                <AlertDescription>Report generated successfully!</AlertDescription>
              </Alert>

              <div class="space-y-3">
                <h3 class="text-white font-medium">Download Reports:</h3>

                <!-- Base Report Download Card -->
                <Card v-if="baseReportUrl" variant="download" color="teal" size="sm" class="border-teal-500/30">
                  <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-white">Base Report</p>
                    <p class="text-xs text-gray-400">Contains detailed assessment data</p>
                  </div>
                  <Button
                    :as="'a'"
                    :href="baseReportUrl"
                    download
                    variant="generalAction"
                    size="skillButton"
                  >
                    Download
                  </Button>
                  </div>
                </Card>

                <!-- Score Report Download Card -->
                <Card v-if="scoreReportUrl" variant="download" color="cyan" size="sm" class="border-cyan-500/30">
                  <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-cyan-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  <div class="flex-1">
                    <p class="text-white">Score Report</p>
                    <p class="text-xs text-gray-400">Contains score summaries and statistics</p>
                  </div>
                  <Button
                    :as="'a'"
                    :href="scoreReportUrl"
                    download
                    variant="generalAction"
                    size="skillButton"
                  >
                    Download
                  </Button>
                  </div>
                </Card>
              </div>
            </div>

            <!-- Submit button -->
            <div class="flex justify-end">
              <Button
                type="submit"
                variant="reportGenerate"
                size="skillButton"
                :disabled="isLoading"
              >
                <span class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Generate Report
                </span>
              </Button>
            </div>
          </form>
    </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import axios from 'axios';
import { HerbitProfessionalLayout } from '@/components/layout';
import { FormCard } from '@/components/ui/form-card';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Get today's date in DD-MM-YYYY format
const today = computed(() => {
  const date = new Date();
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
});

// Form data
const reportDate = ref(today.value);
const quizType = ref('mock');
const isLoading = ref(false);
const errorMessage = ref('');
const reportGenerated = ref(false);
const baseReportUrl = ref('');
const scoreReportUrl = ref('');

// Generate report function
const generateReport = async () => {
  isLoading.value = true;
  errorMessage.value = '';
  reportGenerated.value = false;
  baseReportUrl.value = '';
  scoreReportUrl.value = '';

  try {
    const date = reportDate.value || today.value;

    // Call the API to generate the report
    const response = await axios.post('/api/admin/reports', {
      report_type: 'date_wise',
      report_date: date,
      quiz_type: quizType.value
    });

    if (response.data.message && !response.data.base_report && !response.data.score_report) {
      // No data found for the selected date
      errorMessage.value = response.data.message;
      return;
    }

    reportGenerated.value = true;

    // Create blob URLs for the reports
    if (response.data.base_report) {
      const blob = new Blob([response.data.base_report], { type: 'text/csv' });
      baseReportUrl.value = URL.createObjectURL(blob);
    }

    if (response.data.score_report) {
      const blob = new Blob([response.data.score_report], { type: 'text/csv' });
      scoreReportUrl.value = URL.createObjectURL(blob);
    }

  } catch (error) {
    errorMessage.value = error.message || 'An error occurred while generating the report';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* No need for animation styles as they're now in HerbitBackground component */
</style>

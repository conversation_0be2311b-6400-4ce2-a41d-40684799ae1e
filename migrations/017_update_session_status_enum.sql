-- Update sessions table to support 'assigned' status
-- This migration adds 'assigned' to the status enum constraint

-- First, drop the existing constraint
ALTER TABLE sessions DROP CONSTRAINT IF EXISTS sessions_status_check;

-- Add the new constraint with 'assigned' status
ALTER TABLE sessions ADD CONSTRAINT sessions_status_check 
CHECK (status IN ('assigned', 'pending', 'in_progress', 'completed', 'expired'));

-- Update any existing 'pending' sessions to 'assigned' status
UPDATE sessions SET status = 'assigned' WHERE status = 'pending';
